module ApplicationHelper
  def render_turbo_stream_flash_messages
    turbo_stream.prepend "toast-container", partial: "shared/flash"
  end

  # Generate unique button ID for AI generation buttons
  def lyrics_generation_button_id(button_type)
    "lyrics_generation_button_#{button_type}"
  end

  # Generate LLM stream channel ID for tab-specific targeting
  # @param request_id [String] Unique request identifier
  # @return [String] LLM stream channel ID
  def llm_stream_channel_id(request_id)
    "llm_stream_#{request_id}"
  end

  # Generate unique request ID for forms
  # @return [String] UUID for request tracking
  def generate_request_id
    SecureRandom.uuid
  end

  # Generate placeholder text for different target types
  def placeholder_for_target(target_id)
    case target_id
    when "generation_lyrics"
      "Enter lyrics or use Generate button..."
    when "generation_description", "song_description"
      "Click Surprise Me button to generate creative description..."
    else
      "Click generate button to start..."
    end
  end

  # Render a Flowbite icon with optional HTML attributes
  # @param icon_name [String] Icon name in format 'icon-name-variant' (e.g., 'face-explode-outline')
  # @param options [Hash] HTML attributes to apply to the SVG element
  # @return [String] HTML safe SVG content
  def flowbite_icon(icon_name, options = {})
    svg_content = FlowbiteIconResolver.resolve(icon_name)

    if svg_content.present?
      # Parse the SVG and merge attributes
      process_svg_with_attributes(svg_content, options)
    else
      # Fallback: log warning and return a default icon or empty span
      Rails.logger.warn "Flowbite icon not found: #{icon_name}"
      fallback_icon(icon_name, options)
    end
  end

  def show_sidebar?
    true

    # # 不显示侧边栏的控制器和动作
    # excluded_controllers = %w[sessions]
    # excluded_actions = {
    #   "home" => %w[index]
    # }

    # controller_name = params[:controller]
    # action_name = params[:action]

    # # 如果控制器在排除列表中，不显示侧边栏
    # return false if excluded_controllers.include?(controller_name)

    # # 如果控制器和动作在排除列表中，不显示侧边栏
    # return false if excluded_actions[controller_name]&.include?(action_name)

    # # 其他情况显示侧边栏
    # true
  end

  def current_page_class(path)
    current_page?(path) ? "bg-gradient-to-r from-purple-100 via-pink-100 to-red-100 dark:from-purple-900/30 dark:via-pink-900/30 dark:to-red-900/30 text-purple-900 dark:text-purple-100 shadow-sm border border-purple-200/50 dark:border-purple-700/50" : "text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-purple-50 hover:via-pink-50 hover:to-red-50 dark:hover:from-gray-700/50 dark:hover:via-gray-700/50 dark:hover:to-gray-700/50"
  end

  def format_generation_estimate(user)
    count = user.songs_can_generate
    case count
    when 0
      "No songs available"
    when 1
      "1 song available"
    else
      "#{count} songs available"
    end
  end

  private

  # Process SVG content and merge HTML attributes
  # @param svg_content [String] Raw SVG content
  # @param options [Hash] HTML attributes to merge
  # @return [String] HTML safe processed SVG
  def process_svg_with_attributes(svg_content, options)
    # Parse the SVG to extract and merge attributes
    doc = Nokogiri::HTML::DocumentFragment.parse(svg_content)
    svg_element = doc.at_css("svg")

    return svg_content.html_safe unless svg_element

    # Merge CSS classes
    existing_class = svg_element["class"]
    new_class = options[:class] || options["class"]

    if new_class.present?
      merged_class = [ existing_class, new_class ].compact.join(" ")
      svg_element["class"] = merged_class
    end

    # Merge other attributes (excluding class which we handled above)
    options_without_class = options.except(:class, "class")
    options_without_class.each do |key, value|
      svg_element[key.to_s] = value
    end

    doc.to_html.html_safe
  end

  # Provide fallback when icon is not found
  # @param icon_name [String] Original icon name
  # @param options [Hash] HTML attributes
  # @return [String] HTML safe fallback content
  def fallback_icon(icon_name, options)
    # Return a simple placeholder span
    css_classes = options[:class] || options["class"] || "w-4 h-4"
    content_tag(:span, "?", class: "inline-block text-center #{css_classes}",
                title: "Icon not found: #{icon_name}")
  end
end
