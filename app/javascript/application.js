// Configure your import map in config/importmap.rb. Read more: https://github.com/rails/importmap-rails
import "@hotwired/turbo-rails";
import "controllers";
import "flowbite";

Turbo.StreamActions.llm_update = function () {
  const targetId = this.getAttribute("target");
  const targetEl = document.getElementById(targetId);
  if (!targetEl) return;
  const payload = this.querySelector("template").content.textContent;
  targetEl.value = payload;
  targetEl.dispatchEvent(new Event("input"));
};

Turbo.StreamActions.smart_preset_update = function () {
  const containerId = this.getAttribute("target");
  const container = document.getElementById(containerId);

  if (!container) {
    console.error(`Container with id "${containerId}" not found`);
    return;
  }

  const fragment = this.querySelector("template").content;

  // Get new suggestions from fragment
  const newSuggestions = Array.from(
    fragment.querySelectorAll('[data-tag-type="suggestion"]'),
  );

  // Get current suggestions in container
  const currentSuggestions = Array.from(
    container.querySelectorAll('[data-tag-type="suggestion"]'),
  );

  // Check if we need to update by comparing values and order
  const currentValues = currentSuggestions.map((pill) => pill.dataset.value);
  const newValues = newSuggestions.map((pill) => pill.dataset.value);

  // Skip update if arrays are identical (same values in same order)
  if (
    currentValues.length === newValues.length &&
    currentValues.every((value, index) => value === newValues[index])
  ) {
    return; // No update needed
  }

  // Remove all current suggestions
  currentSuggestions.forEach((pill) => pill.remove());

  // Add new suggestions (controller will handle visibility based on selected values)
  newSuggestions.forEach((suggestion) => {
    container.appendChild(suggestion.cloneNode(true));
  });

  // Find the preset search controller and trigger local filtering
  const presetSearchElement = container.closest(
    '[data-controller*="preset-search"]',
  );
  if (presetSearchElement) {
    // Get the Stimulus controller instance and call the method directly
    // Note: In Turbo Stream Actions, 'this' refers to the <turbo-stream> element,
    // not a Stimulus controller, so 'this.application' is always undefined.
    // We use window.Stimulus which is set in controllers/application.js
    const application = window.Stimulus;
    if (application) {
      const controller = application.getControllerForElementAndIdentifier(
        presetSearchElement,
        "preset-search",
      );
      if (controller?.updateSuggestionsLocally) {
        controller.updateSuggestionsLocally();
      }
    } else {
      console.error(
        "Stimulus application not available on window.Stimulus - preset search local filtering will not work",
      );
    }
  }
};
