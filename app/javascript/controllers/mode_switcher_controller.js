import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = [
    "descriptionTab",
    "parametersTab",
    "descriptionMode",
    "parametersMode",
    "descriptionHelp",
    "parametersHelp",
  ];
  static values = { current: String };

  connect() {
    console.log("ModeSwitcherController connected", this.element);
    // Initialize with the current value or default to description
    const initialMode = this.currentValue || "description";
    if (initialMode === "description") {
      this.switchToDescription();
    } else {
      this.switchToParameters();
    }
  }

  switchToDescription() {
    this.currentValue = "description";

    // Update tab styling with smooth transitions
    this.updateTabStyling("description");

    // Show/hide content sections with fade effect
    this.showModeContent("description");

    // Update help text
    this.updateHelpText("description");

    console.log("Switched to description mode");
  }

  switchToParameters() {
    this.currentValue = "parameters";

    // Update tab styling with smooth transitions
    this.updateTabStyling("parameters");

    // Show/hide content sections with fade effect
    this.showModeContent("parameters");

    // Update help text
    this.updateHelpText("parameters");

    console.log("Switched to parameters mode");
  }

  updateTabStyling(activeMode) {
    try {
      if (activeMode === "description") {
        this.descriptionTabTarget.dataset.modeActive = "true";
        this.parametersTabTarget.dataset.modeActive = "false";
      } else {
        this.parametersTabTarget.dataset.modeActive = "true";
        this.descriptionTabTarget.dataset.modeActive = "false";
      }
    } catch (error) {
      console.error("Error updating tab styling:", error);
    }
  }

  showModeContent(activeMode) {
    try {
      if (activeMode === "description") {
        // Show description mode
        this.descriptionModeTarget.classList.remove("hidden");
        this.descriptionModeTarget.classList.add(
          "animate-in",
          "fade-in",
          "duration-200",
        );

        // Hide parameters mode
        this.parametersModeTarget.classList.add("hidden");
        this.parametersModeTarget.classList.remove(
          "animate-in",
          "fade-in",
          "duration-200",
        );
      } else {
        // Show parameters mode
        this.parametersModeTarget.classList.remove("hidden");
        this.parametersModeTarget.classList.add(
          "animate-in",
          "fade-in",
          "duration-200",
        );

        // Hide description mode
        this.descriptionModeTarget.classList.add("hidden");
        this.descriptionModeTarget.classList.remove(
          "animate-in",
          "fade-in",
          "duration-200",
        );
      }
    } catch (error) {
      console.error("Error showing mode content:", error);
    }
  }

  updateHelpText(activeMode) {
    try {
      if (activeMode === "description") {
        this.descriptionHelpTarget.classList.remove("hidden");
        this.parametersHelpTarget.classList.add("hidden");
      } else {
        this.parametersHelpTarget.classList.remove("hidden");
        this.descriptionHelpTarget.classList.add("hidden");
      }
    } catch (error) {
      console.error("Error updating help text:", error);
    }
  }
}
