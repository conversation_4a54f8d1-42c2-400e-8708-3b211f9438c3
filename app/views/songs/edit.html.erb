<!-- Edit Mode with Compact Layout -->
<%= turbo_frame_tag "song_details" do %>
  <!-- Compact Layout Container -->
  <div class="flex-1 flex flex-col">
    <!-- Top Section: Cover + Title + Actions -->
    <div class="p-4 border-b border-purple-200/30 dark:border-gray-700">
      <!-- Desktop Layout -->
      <div class="hidden md:flex items-start space-x-4">
        <!-- Cover Image (Left) -->
        <div class="flex-shrink-0">
          <%= render "songs/song_cover", song: @song %>
        </div>
        <!-- Title + Status + Actions (Right) -->
        <div class="flex-1 min-w-0">
          <!-- Title Row (Edit Mode) -->
          <div class="mb-2">
            <%= render "songs/song_title_section", song: @song, edit_mode: true %>
          </div>
          <!-- Status Row -->
          <%= render "songs/song_status", song: @song %>
          <!-- Action Buttons Row -->
          <div class="mt-3">
            <%= render "songs/song_actions", song: @song %>
          </div>
        </div>
      </div>
      <!-- Mobile Layout -->
      <div class="md:hidden space-y-4">
        <!-- Cover Image (Centered) -->
        <div class="flex justify-center">
          <%= render "songs/song_cover", song: @song %>
        </div>
        <!-- Title + Actions -->
        <div class="space-y-3">
          <!-- Title Row (Edit Mode) -->
          <div>
            <%= render "songs/song_title_section", song: @song, edit_mode: true %>
          </div>
          <!-- Status Row -->
          <div class="flex justify-center">
            <%= render "songs/song_status", song: @song %>
          </div>
          <!-- Action Buttons Row -->
          <div class="flex justify-center">
            <%= render "songs/song_actions", song: @song %>
          </div>
        </div>
      </div>
    </div>
    <!-- Details Section -->
    <div class="px-4 py-3 border-b border-purple-200/30 dark:border-gray-700">
      <%= render "songs/song_details", song: @song %>
    </div>
    <!-- Lyrics Section -->
    <div class="flex-1">
      <%= render "songs/song_lyrics", song: @song %>
    </div>
  </div>
<% end %>
