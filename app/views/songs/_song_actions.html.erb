<!-- Compact Action Buttons -->
<div class="flex items-center space-x-2">
  <!-- Play Button -->
  <% if song.has_stream_audio? || song.has_final_audio? %>
    <%= link_to song_path(song),
        data: { turbo_frame: "song_player" },
        class: "inline-flex items-center justify-center w-8 h-8 text-white bg-purple-600 rounded-full hover:bg-purple-700 focus:ring-2 focus:outline-none focus:ring-purple-300 transition-all hover:scale-105",
        title: "Play song" do %>
      <%= flowbite_icon('play-outline', class: 'size-4') %>
    <% end %>
  <% else %>
    <button type="button" disabled
                class="inline-flex items-center justify-center w-8 h-8 text-gray-400 bg-gray-200 dark:bg-gray-700 rounded-full cursor-not-allowed"
                title="Processing...">
      <%= flowbite_icon('clock-outline', class: 'size-4') %>
    </button>
  <% end %>
  <!-- Download Button -->
  <% if song.has_final_audio? %>
    <a href="<%= song.audio_url %>" target="_blank" download="<%= download_filename_for_song(song) %>"
           class="inline-flex items-center justify-center w-8 h-8 text-green-600 hover:text-white hover:bg-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400 dark:hover:bg-green-600 rounded-full focus:ring-2 focus:outline-none focus:ring-green-300 transition-all hover:scale-105"
           title="Download song">
      <%= flowbite_icon('download-outline', class: 'size-4') %>
    </a>
  <% elsif song.has_stream_audio? %>
    <button type="button" disabled
                class="inline-flex items-center justify-center w-8 h-8 text-gray-400 bg-gray-200 dark:bg-gray-700 rounded-full cursor-not-allowed"
                title="Processing...">
      <%= render "shared/loading_icon", class: 'size-4 animate-spin' %>
    </button>
  <% else %>
    <button type="button" disabled
                class="inline-flex items-center justify-center w-8 h-8 text-gray-400 bg-gray-200 dark:bg-gray-700 rounded-full cursor-not-allowed"
                title="Generating...">
      <%= flowbite_icon('clock-outline', class: 'size-4') %>
    </button>
  <% end %>
  <!-- Delete Button -->
  <%= button_to song,
          class: "inline-flex items-center justify-center w-8 h-8 text-red-600 hover:text-white hover:bg-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-600 rounded-full focus:ring-2 focus:outline-none focus:ring-red-300 transition-all hover:scale-105",
          method: :delete,
          title: "Delete song",
          form: { data: { turbo_confirm: "Are you sure you want to delete '#{song.title}'? This action cannot be undone." } } do %>
    <%= flowbite_icon('trash-bin-outline', class: 'size-4') %>
  <% end %>
</div>
