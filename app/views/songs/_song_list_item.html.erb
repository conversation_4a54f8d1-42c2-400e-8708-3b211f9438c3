<%= turbo_frame_tag dom_id(song, :song_list) do %>
  <li class="@container group py-3 px-4">
    <div class="flex items-center gap-4">
      <!-- Cover Image -->
      <div class="shrink-0">
        <% if song.has_stream_audio? || song.has_final_audio? %>
          <!-- Playable Song - Cover as Button -->
          <%= link_to song_path(song), 
            data: { turbo_frame: "song_player" },
            class: "relative w-12 h-12 rounded-lg overflow-hidden group/cover cursor-pointer focus:ring-4 focus:outline-none focus:ring-blue-300 block" do %>
            <%= render "songs/song_list_cover", song: song %>
            <!-- Play Icon Overlay - Enhanced for mobile -->
            <div class="absolute inset-0 bg-black flex items-center justify-center transition-all duration-200 pointer-coarse:bg-opacity-30 pointer-fine:bg-opacity-50 pointer-fine:opacity-0 pointer-fine:group-hover/cover:opacity-100">
              <%= flowbite_icon('play-outline', class: 'size-5 text-white') %>
            </div>
          <% end %>
        <% else %>
          <!-- Non-playable Song - Regular Cover -->
          <%= render "songs/song_list_cover", song: song %>
        <% end %>
      </div>
      <!-- Song Info -->
      <div class="flex-1 min-w-0">
        <div class="flex items-center gap-2">
          <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
            <%= turbo_frame_tag  dom_id(song, :list_title), class: "contents" do %>
              <%= song.title %>
            <% end %>
          </p>
        </div>
        <div class="flex items-center gap-2">
          <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
            <%= song.duration_formatted || "Streaming..." %>
          </p>
          <!-- In Progress Indicator -->
          <% if song.has_stream_audio? && !song.has_final_audio? %>
            <div class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 whitespace-nowrap">
              <%= render "shared/loading_icon", class: 'size-3 mr-1 animate-spin' %>
              In Progress
            </div>
          <% end %>
        </div>
      </div>
      <!-- Actions -->
      <div class="inline-flex items-center gap-1">
        <!-- Core Action: Favorite Button - Always visible on touch, hover on mouse -->
        <%= render "songs/list_favorite_button", song: song %>
        <!-- Core Action: View Details Button - Always visible on touch, hover on mouse -->
        <%= link_to song_path(song),
                data: { turbo_frame: "song_details" },
                class: "hidden items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-blue-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all @xs:inline-flex" do %>
          <%= flowbite_icon('arrow-right-to-bracket-outline', class: 'size-3.5') %>
        <% end %>
        <!-- More Actions Dropdown -->
        <div class="relative">
          <button type="button" 
                  class="inline-flex items-center p-1 pointer-coarse:p-1.5 text-sm font-medium text-center text-gray-400 rounded-md hover:bg-gray-100 hover:text-gray-600 focus:ring-2 focus:outline-none focus:ring-gray-300 transition-all"
                  data-dropdown-toggle="<%= dom_id(song, :actions_dropdown) %>"
                  data-dropdown-placement="left-start"
                  aria-expanded="false">
            <%= flowbite_icon('dots-vertical', class: 'size-3.5') %>
            <span class="sr-only">More actions</span>
          </button>
          <!-- Dropdown Menu -->
          <div id="<%= dom_id(song, :actions_dropdown) %>" 
               class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 dark:divide-gray-600">
            <ul class="py-2 text-sm text-gray-700 dark:text-gray-200">
              <!-- View Details - Only show in dropdown when hidden in main actions -->
              <li class="@sm:hidden">
                <a href="<%= song_path(song) %>" 
                   data-turbo-frame="song_details"
                   class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                  <%= flowbite_icon('arrow-right-to-bracket-outline', class: 'size-4 mr-2') %>
                  View Details
                </a>
              </li>
              <% if song.has_final_audio? %>
                <li>
                  <a href="<%= song.audio_url %>" 
                     target="_blank" 
                     download="<%= download_filename_for_song(song) %>"
                     class="flex items-center px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
                    <%= flowbite_icon('download-outline', class: 'size-4 mr-2') %>
                    Download
                  </a>
                </li>
              <% else %>
                <li>
                  <div class="flex items-center px-4 py-2 text-gray-400 dark:text-gray-500 cursor-not-allowed">
                    <% if song.has_stream_audio? %>
                      <%= flowbite_icon('download-outline', class: 'size-4 mr-2') %>
                      Download
                    <% else %>
                      <%= render "shared/loading_icon", class: 'size-4 mr-2 animate-spin' %>
                      Processing...
                    <% end %>
                  </div>
                </li>
              <% end %>
            </ul>
            <div class="py-2">
              <%= button_to song, 
                    method: :delete,
                    form: { 
                      data: { turbo_confirm: "Are you sure you want to delete '#{song.title}'? This action cannot be undone." },
                      class: "w-full"
                    },
                    class: "flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-red-500 dark:hover:text-red-400",
                    title: "Delete song" do %>
                <%= flowbite_icon('trash-bin-outline', class: 'size-4 mr-2') %>
                Delete
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </li>
<% end %>