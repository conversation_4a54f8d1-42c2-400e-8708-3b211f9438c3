<!-- Update song title in list item -->
<%= turbo_stream.update dom_id(@song, :list_title) do %>
  <%= @song.title %>
<% end %>
<!-- Update song title in details panel -->
<%= turbo_stream.update dom_id(@song, :info_title) do %>
  <%= @song.title %>
<% end %>
<!-- Update song title in player if currently playing -->
<%= turbo_stream.update dom_id(@song, :player_title) do %>
  <%= @song.title %>
<% end %>
<!-- Replace edit form with song info view -->
<%= turbo_stream.update "song_details", method: :morph do %>
  <%= render "songs/song_info", song: @song %>
<% end %>
<!-- Show success message -->
<%= render_turbo_stream_flash_messages %>
