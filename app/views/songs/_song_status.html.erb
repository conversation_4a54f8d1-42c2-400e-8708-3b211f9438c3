<!-- Compact Status Indicator -->
<% unless song.has_final_audio? %>
  <div class="flex items-center mt-1">
    <div class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <%=
          case song.generation_task.status
          when 'failed'
            'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
          when 'pending'
            'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400'
          else
            'bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400'
          end %>">
      <%= case song.generation_task.status
              when 'failed'
                if song.generation_task.error_data&.dig('error') == '任务超时'
                  flowbite_icon('clock-outline', class: 'size-3 mr-1')
                else
                  flowbite_icon('exclamation-circle-outline', class: 'size-3 mr-1')
                end
              when 'pending'
                flowbite_icon('clock-outline', class: 'size-3 mr-1')
              else
                render "shared/loading_icon", class: 'size-3 mr-1 animate-spin'
              end %>
      <%= if song.generation_task.status == 'failed' && song.generation_task.error_data&.dig('error') == '任务超时'
            '任务超时'
          else
            song.generation_task.status.humanize
          end %>
    </div>
  </div>
<% end %>
