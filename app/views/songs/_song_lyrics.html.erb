<!-- Lyrics Section -->
<div class="p-4 flex-1 flex flex-col">
  <div class="flex items-center justify-between mb-3">
    <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100">Lyrics</h3>
    <% if song.has_lyrics? && song.lyrics_text.present? %>
      <button type="button"
              data-collapse-toggle="lyrics-content"
              aria-expanded="true"
              aria-controls="lyrics-content"
              class="inline-flex items-center p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-all">
        <%= flowbite_icon('chevron-down-outline', class: 'size-4 transform transition-transform duration-200', id: 'lyrics-chevron') %>
      </button>
    <% end %>
  </div>
  <div id="lyrics-content" class="flex-1">
    <% if song.has_lyrics? && song.lyrics_text.present? %>
      <div class="bg-gradient-to-br from-purple-50/50 via-pink-50/30 to-red-50/50 dark:from-gray-800/50 dark:via-gray-700/30 dark:to-gray-800/50 rounded-lg p-4 max-h-64 overflow-y-auto border border-purple-200/30 dark:border-gray-600">
        <div class="text-gray-700 dark:text-gray-300 text-sm whitespace-pre-line leading-relaxed">
          <%= song.lyrics_text %>
        </div>
      </div>
    <% elsif song.generation_task.instrumental? %>
      <div class="bg-gradient-to-br from-purple-50/50 via-pink-50/30 to-red-50/50 dark:from-gray-800/50 dark:via-gray-700/30 dark:to-gray-800/50 rounded-lg p-4 text-center border border-purple-200/30 dark:border-gray-600">
        <div class="text-gray-500 dark:text-gray-400 text-sm flex items-center justify-center">
          <%= flowbite_icon('music-outline', class: 'size-5 mr-2 text-purple-500 dark:text-purple-400') %>
          This is an instrumental track
        </div>
      </div>
    <% elsif song.generation_task.pending? || song.generation_task.in_progress? %>
      <div class="bg-gradient-to-br from-purple-50/50 via-pink-50/30 to-red-50/50 dark:from-gray-800/50 dark:via-gray-700/30 dark:to-gray-800/50 rounded-lg p-4 text-center border border-purple-200/30 dark:border-gray-600">
        <div class="text-gray-500 dark:text-gray-400 text-sm flex items-center justify-center">
          <%= render "shared/loading_icon", class: 'size-5 mr-2 animate-spin text-purple-500 dark:text-purple-400' %>
          Lyrics being generated...
        </div>
      </div>
    <% else %>
      <div class="bg-gradient-to-br from-purple-50/50 via-pink-50/30 to-red-50/50 dark:from-gray-800/50 dark:via-gray-700/30 dark:to-gray-800/50 rounded-lg p-4 text-center border border-purple-200/30 dark:border-gray-600">
        <div class="text-gray-500 dark:text-gray-400 text-sm flex items-center justify-center">
          <%= flowbite_icon('file-text-outline', class: 'size-5 mr-2 text-purple-500 dark:text-purple-400') %>
          No lyrics available
        </div>
      </div>
    <% end %>
  </div>
</div>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const toggleButton = document.querySelector('[data-collapse-toggle="lyrics-content"]');
    const content = document.getElementById('lyrics-content');
    const chevron = document.getElementById('lyrics-chevron');

    if (toggleButton && content && chevron) {
      toggleButton.addEventListener('click', function() {
        const isExpanded = toggleButton.getAttribute('aria-expanded') === 'true';

        if (isExpanded) {
          content.style.display = 'none';
          toggleButton.setAttribute('aria-expanded', 'false');
          chevron.style.transform = 'rotate(-90deg)';
        } else {
          content.style.display = 'block';
          toggleButton.setAttribute('aria-expanded', 'true');
          chevron.style.transform = 'rotate(0deg)';
        }
      });
    }
  });
</script>
