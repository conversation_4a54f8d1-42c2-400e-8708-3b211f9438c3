<% edit_mode = local_assigns.fetch(:edit_mode, false) %>
<% if edit_mode %>
  <!-- Edit Form -->
  <%= form_with model: song, local: false, class: "w-full" do |form| %>
    <div class="mb-3">
      <%= form.text_field :title,
            value: song.title,
            id: "song_title",
            class: "bg-transparent border-0 border-b-2 border-purple-300 dark:border-gray-600 text-gray-900 dark:text-gray-100 text-lg font-semibold rounded-none focus:ring-0 focus:border-purple-500 dark:focus:border-purple-400 block w-full p-2 placeholder-gray-400",
            placeholder: "Enter song title",
            maxlength: 100,
            required: true %>
      <% if song.errors[:title].any? %>
        <p class="mt-1 text-xs text-red-600"><%= song.errors[:title].first %></p>
      <% end %>
    </div>
    <div class="flex space-x-2">
      <%= form.submit "Save",
            class: "inline-flex items-center px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 rounded-full hover:from-purple-700 hover:via-pink-700 hover:to-red-700 focus:ring-2 focus:outline-none focus:ring-purple-300 transition-all" %>
      <%= link_to "Cancel",
            song_path(song),
            data: { turbo_frame: "song_details" },
            class: "inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-gray-100 focus:outline-none bg-transparent hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-all" %>
    </div>
  <% end %>
<% else %>
  <!-- Display Mode -->
  <div class="flex items-center justify-between w-full">
    <h2 class="text-lg font-semibold text-gray-900 dark:text-gray-100 truncate flex-1 mr-2">
      <%= turbo_frame_tag dom_id(song, :info_title), class: "contents" do %>
        <%= song.title %>
      <% end %>
    </h2>
    <div class="flex items-center space-x-1 flex-shrink-0">
      <%= link_to edit_song_path(song),
            data: { turbo_frame: "song_details" },
            class: "inline-flex items-center justify-center w-8 h-8 text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 hover:bg-purple-50 dark:hover:bg-gray-700 rounded-full focus:ring-2 focus:outline-none focus:ring-purple-300 transition-all",
            title: "Edit song title" do %>
        <%= flowbite_icon('edit-outline', class: 'size-4') %>
      <% end %>
      <%= render "songs/favorite_button", song: song %>
    </div>
  </div>
<% end %>
