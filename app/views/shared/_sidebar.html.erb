<button data-drawer-target="default-sidebar" data-drawer-toggle="default-sidebar" aria-controls="default-sidebar" type="button" class="fixed top-4 left-4 z-50 inline-flex items-center p-2 text-sm text-gray-500 rounded-lg md:hidden hover:bg-purple-100 hover:text-purple-600 focus:outline-none focus:ring-2 focus:ring-purple-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600 transition-all duration-200 bg-white dark:bg-gray-800 shadow-lg">
  <span class="sr-only">Open sidebar</span>
  <%= flowbite_icon('bars-outline', class: 'size-6') %>
</button>
<aside id="default-sidebar" class="fixed top-0 left-0 z-40 w-64 h-screen transition-transform -translate-x-full md:translate-x-0 bg-gradient-to-b from-purple-50 via-pink-50 to-red-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-800" aria-label="Sidebar">
  <!-- Main Content (Scrollable) -->
  <div class="overflow-y-auto py-4 px-3 h-full border-r border-purple-200/30 dark:border-gray-700 pb-32">
    <!-- Logo Section -->
    <%= link_to root_path, class: "flex items-center pl-2 mb-5 group transition-all duration-300 hover:scale-105" do %>
      <div class="size-8 bg-gradient-to-r from-purple-400 via-pink-500 to-red-500 rounded-full flex items-center justify-center mr-3 shadow-lg group-hover:shadow-xl transition-shadow duration-300">
        <%= flowbite_icon('music-outline', class: 'size-4 text-white') %>
      </div>
      <span class="self-center text-2xl font-semibold whitespace-nowrap bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 bg-clip-text text-transparent dark:from-purple-400 dark:via-pink-400 dark:to-red-400">Musicfy.me</span>
    <% end %>
    <!-- Main Navigation -->
    <ul class="space-y-2">
      <li>
        <%= link_to root_path, class: "flex items-center p-3 text-base font-medium rounded-xl group transition-all duration-200 hover:scale-105 #{current_page_class(root_path)}" do %>
          <%= flowbite_icon('home-outline', class: 'size-5 text-gray-500 transition-all duration-200 dark:text-gray-400 group-hover:text-purple-600 dark:group-hover:text-purple-400') %>
          <span class="ml-3">Home</span>
        <% end %>
      </li>
      <li>
        <%= link_to songs_path, class: "flex items-center p-3 text-base font-medium rounded-xl group transition-all duration-200 hover:scale-105 #{current_page_class(songs_path)}" do %>
          <%= flowbite_icon('music-outline', class: 'flex-shrink-0 size-5 text-gray-500 transition-all duration-200 dark:text-gray-400 group-hover:text-pink-600 dark:group-hover:text-pink-400') %>
          <span class="flex-1 ml-3 whitespace-nowrap">My Songs</span>
        <% end %>
      </li>
      <li>
        <%= link_to plans_path, class: "flex items-center p-3 text-base font-medium rounded-xl group transition-all duration-200 hover:scale-105 #{current_page_class(plans_path)}" do %>
          <%= flowbite_icon('shopping-bag-outline', class: 'flex-shrink-0 size-5 text-gray-500 transition-all duration-200 dark:text-gray-400 group-hover:text-red-600 dark:group-hover:text-red-400') %>
          <span class="flex-1 ml-3 whitespace-nowrap">Plans</span>
        <% end %>
      </li>
    </ul>
  </div>
  <!-- Credit Usage Section -->
  <% if authenticated? %>
    <div class="absolute bottom-16 left-0 w-full px-3">
      <%= render "shared/credit_usage" %>
    </div>
  <% end %>
  <!-- Footer Controls -->
  <div class="absolute bottom-0 left-0 z-20 w-full bg-gradient-to-r from-purple-50/80 via-pink-50/80 to-red-50/80 dark:from-gray-800/90 dark:via-gray-800/90 dark:to-gray-800/90 backdrop-blur-sm border-t border-purple-200/30 dark:border-gray-700">
    <div class="flex items-center justify-between p-4 space-x-4">
      <!-- Language Toggle -->
      <button type="button" class="inline-flex items-center p-2 text-sm font-medium text-gray-500 rounded-lg cursor-pointer hover:text-purple-600 dark:hover:text-purple-400 dark:text-gray-400 transition-colors duration-200" data-dropdown-toggle="language-dropdown">
        <%= flowbite_icon('language-outline', class: 'mr-1 size-4') %>
        <span class="hidden md:inline">EN</span>
      </button>
      <!-- Language Dropdown -->
      <div class="hidden z-50 my-4 text-base list-none bg-white rounded divide-y divide-gray-100 shadow dark:bg-gray-700" id="language-dropdown">
        <ul class="py-1" role="none">
          <li>
            <a href="#" class="block py-2 px-4 text-sm text-gray-700 hover:bg-gray-100 dark:hover:text-white dark:text-gray-300 dark:hover:bg-gray-600" role="menuitem">
              <div class="inline-flex items-center">
                English (US)
              </div>
            </a>
          </li>
        </ul>
      </div>
      <!-- User Menu -->
      <%= render "shared/user_menu" %>
    </div>
  </div>
</aside>