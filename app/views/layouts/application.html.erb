<!DOCTYPE html>
<html>
  <head>
    <title><%= content_for(:title) || "Musicfy Me" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= yield :head %>
    <%# Enable PWA manifest for installable apps (make sure to enable in config/routes.rb too!) %>
    <%#= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>
    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">
    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <link rel="stylesheet" href="https://cdn.vidstack.io/player.css" />
    <%= javascript_importmap_tags %>
    <script src="https://cdn.vidstack.io/player" type="module"></script>
    <%= javascript_include_tag "application", "data-turbo-track": "reload", type: "module" %>
  </head>
  <body class="bg-gray-50 dark:bg-gray-900">
    <% if show_sidebar? %>
      <%= render "shared/sidebar" %>
      <div class="md:ml-64 pt-20 md:pt-8">
        <%= yield %>
      </div>
    <% else %>
      <%= yield %>
    <% end %>
    <%= render "shared/flash" %>
  </body>
</html>
