        <!-- Submit Section -->
<div class="pt-3 border-t border-purple-200/30 dark:border-gray-700">
  <div class="text-center mb-3">
    <div class="text-xs text-gray-500 dark:text-gray-400">Generation Cost</div>
    <div class="text-sm font-semibold text-gray-900 dark:text-white"><%= generation_cost %> Credits</div>
    <div class="text-xs text-gray-500 dark:text-gray-400">
      <% remaining_credits = current_credits - generation_cost %>
      <% if remaining_credits >= 0 %>
        <%= remaining_credits %> remaining after generation
      <% else %>
        <span class="text-red-600 dark:text-red-400">Insufficient credits</span>
      <% end %>
    </div>
  </div>
  <div class="space-y-2">
    <% if current_credits < generation_cost %>
      <%= link_to plans_path(next: request.referrer),
                  data: { turbo_frame: "_top" },
            class: "w-full bg-yellow-600 hover:bg-yellow-700 dark:bg-yellow-500 dark:hover:bg-yellow-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 text-center block text-sm shadow-sm hover:shadow-md" do %>
        Get More Credits
      <% end %>
    <% end %>
    <%= form.submit "Generate Music", 
              disabled: current_credits < generation_cost,
              class: "w-full bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 hover:from-purple-700 hover:via-pink-700 hover:to-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 text-sm shadow-sm hover:shadow-md" %>
  </div>
</div>