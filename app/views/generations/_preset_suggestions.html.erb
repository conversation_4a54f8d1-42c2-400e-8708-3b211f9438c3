<%# 
  Preset suggestions partial for search results
  
  Parameters:
  - suggestions: Array of suggestion strings
  - field_type: String (genre, mood, instrument, etc.)
  - query: String (current search query)
%>
<%
  suggestions = local_assigns[:suggestions] || []
  field_type = local_assigns[:field_type]
  query = local_assigns[:query] || ""
%>
<% if suggestions.any? %>
  <%# Suggestion buttons %>
  <% suggestions.each do |suggestion| %>
    <button type="button"
            class="inline-flex items-center px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-purple-100 hover:via-pink-100 hover:to-red-100 hover:text-purple-700 dark:hover:from-purple-900/20 dark:hover:via-pink-900/20 dark:hover:to-red-900/20 dark:hover:text-purple-300 rounded border border-gray-200 dark:border-gray-600 hover:border-purple-200 dark:hover:border-purple-700 transition-all duration-200 font-medium"
            data-action="click->preset-search#selectSuggestion"
      data-value="<%= suggestion %>"
      data-tag-type="suggestion">
      <%= suggestion %>
    </button>
  <% end %>
<% elsif query.present? %>
  <%# No suggestions - show add custom option if there's a query %>
  <button type="button"
          class="inline-flex items-center px-2 py-1 text-xs text-purple-600 dark:text-purple-400 hover:text-pink-600 dark:hover:text-pink-400 hover:bg-gradient-to-r hover:from-purple-50 hover:via-pink-50 hover:to-red-50 dark:hover:from-purple-900/20 dark:hover:via-pink-900/20 dark:hover:to-red-900/20 rounded transition-all duration-200 font-medium"
          data-action="click->preset-search#addCustom"
    data-tag-type="suggestion">
    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
    </svg>
    Add "<%= query %>"
  </button>
<% else %>
  <%# Default presets when no input %>
  <% MusicPresets.popular_for_field(field_type).each do |preset| %>
    <button type="button"
            class="inline-flex items-center px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-purple-100 hover:via-pink-100 hover:to-red-100 hover:text-purple-700 dark:hover:from-purple-900/20 dark:hover:via-pink-900/20 dark:hover:to-red-900/20 dark:hover:text-purple-300 rounded border border-gray-200 dark:border-gray-600 hover:border-purple-200 dark:hover:border-purple-700 transition-all duration-200 font-medium"
            data-action="click->preset-search#selectSuggestion"
      data-value="<%= preset %>"
      data-tag-type="suggestion">
      <%= preset %>
    </button>
  <% end %>
<% end %>
