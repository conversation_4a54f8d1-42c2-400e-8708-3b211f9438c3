<% if task.songs.any? %>
  <% 2.times do |index| %>
    <%= turbo_stream.replace generation_task_placeholder_id(task, index) do %>
      <%= render "generations/placeholder", task: task, song_index: index %>
    <% end %>
  <% end %>
  <% task.songs.each_with_index do |song, index| %>
    <%= turbo_stream.replace dom_id(song, :song_list), method: :morph do %>
      <%= render "songs/song_list_item", song: song %>
    <% end %>
    <%= turbo_stream.replace dom_id(song, :song_info), method: :morph do %>
      <%= render "songs/song_info", song: song %>
    <% end %>
    <%= turbo_stream.replace song_stream_player_id(song), method: :morph do %>
      <%= render "songs/player", song: song %>
    <% end %>
  <% end %>
<% elsif task.failed? %>
  <% 2.times do |index| %>
    <%= turbo_stream.replace generation_task_placeholder_id(task, index) do %>
      <%= render "generations/failed_list_item", task: task, song_index: index %>
    <% end %>
  <% end %>
  <!-- Clean up non-list-item elements for failed tasks -->
  <%= turbo_stream.replace "song_player", method: :morph do %>
    <%= render "songs/player" %>
  <% end %>
  <%= turbo_stream.update "song_details" do %>
    <%= render "songs/empty_song" %>
  <% end %>
<% end %>
<%= turbo_stream.replace "song_stats" do %>
  <%= render "songs/song_stats", user: task.user %>
<% end %>
<%= render_turbo_stream_flash_messages %>